// API Configuration - langsung ke backend server (bypass proxy untuk API calls)
const API_BASE_URL = "http://13.214.188.83:3000";

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem("authToken");
};

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// API Service functions
export const apiService = {
  // Register user
  register: async (userData) => {
    try {
      console.log("=== REGISTER API REQUEST ===");
      console.log("Endpoint:", `${API_BASE_URL}/register`);
      console.log("User data:", userData);

      const response = await fetch(`${API_BASE_URL}/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      console.log("=== REGISTER API RESPONSE ===");
      console.log("Status:", response.status);

      const data = await response.json();
      console.log("Response data:", data);

      if (!response.ok) {
        throw new Error(data.message || "Registration failed");
      }

      return data;
    } catch (error) {
      console.error("API Register Error:", error);
      throw error;
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      console.log("=== LOGIN API REQUEST ===");
      console.log("Endpoint:", `${API_BASE_URL}/login`);
      console.log("Credentials:", credentials);

      const response = await fetch(`${API_BASE_URL}/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      console.log("=== LOGIN API RESPONSE ===");
      console.log("Status:", response.status);
      console.log("Status Text:", response.statusText);

      const data = await response.json();
      console.log("Response data:", data);

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      return data;
    } catch (error) {
      console.error("API Login Error:", error);
      throw error;
    }
  },

  // Logout user
  logout: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/logout`, {
        method: "POST",
        headers: getAuthHeaders(),
      });

      console.log("Logout API response:", response.status);
    } catch (error) {
      console.error("Logout API error:", error);
    } finally {
      // Always clear local storage
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");
      console.log("User logged out, token and user data cleared");
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = getAuthToken();
    return !!token;
  },

  // Get current user data
  getCurrentUser: () => {
    const userData = localStorage.getItem("user");
    return userData ? JSON.parse(userData) : null;
  },

  // Example of authenticated request (you can use this pattern for other API calls)
  getProfile: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/profile`, {
        method: "GET",
        headers: getAuthHeaders(),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch profile");
      }

      return data;
    } catch (error) {
      console.error("API Profile Error:", error);
      throw error;
    }
  },

  // Create booking (data_diri)
  createBooking: async (bookingData) => {
    try {
      const bookingEndpoint = `${API_BASE_URL}/booking`;

      console.log("=== BOOKING API REQUEST ===");
      console.log("Endpoint:", bookingEndpoint);
      console.log("Data yang dikirim:", JSON.stringify(bookingData, null, 2));

      const headers = getAuthHeaders();
      console.log("Headers:", headers);

      const response = await fetch(bookingEndpoint, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(bookingData),
      });

      console.log("=== BOOKING API RESPONSE ===");
      console.log("Status:", response.status);
      console.log("Status Text:", response.statusText);
      console.log("Headers:", Object.fromEntries(response.headers.entries()));

      // Cek apakah response adalah JSON
      const contentType = response.headers.get("content-type");
      let data;

      try {
        if (contentType && contentType.includes("application/json")) {
          data = await response.json();
        } else {
          // Jika bukan JSON, ambil sebagai text
          const textData = await response.text();
          console.log("Response text:", textData);
          data = { message: textData, raw: textData };
        }
      } catch (parseError) {
        console.error("Error parsing response:", parseError);
        const textData = await response.text();
        data = { message: "Failed to parse response", raw: textData };
      }

      console.log("Parsed response data:", data);

      if (!response.ok) {
        console.error("=== BOOKING API ERROR ===");
        console.error("Status:", response.status);
        console.error("Status Text:", response.statusText);
        console.error("Response Data:", data);
        console.error("Request Data:", bookingData);
        console.error("Request Headers:", headers);

        // Buat error message yang lebih informatif
        let errorMessage = "Gagal membuat booking";

        if (data.message) {
          errorMessage = data.message;
        } else if (data.raw) {
          errorMessage = `Server Error: ${data.raw}`;
        } else if (response.status === 500) {
          errorMessage =
            "Server mengalami kesalahan internal. Silakan coba lagi atau hubungi admin.";
        } else {
          errorMessage = `HTTP Error ${response.status}: ${response.statusText}`;
        }

        throw new Error(errorMessage);
      }

      console.log("=== BOOKING API SUCCESS ===");
      return data;
    } catch (error) {
      console.error("=== BOOKING API EXCEPTION ===");
      console.error("Error type:", error.constructor.name);
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);

      if (error.name === "TypeError" && error.message.includes("fetch")) {
        throw new Error(
          "Gagal terhubung ke server. Periksa koneksi internet Anda."
        );
      }
      throw new Error(
        error.message || "Gagal membuat booking. Silakan coba lagi."
      );
    }
  },

  // Booking activities
  bookingActivities: async (bookingData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/booking/activities`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(bookingData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Booking activities failed");
      }

      return data;
    } catch (error) {
      console.error("Booking activities error:", error);
      throw error;
    }
  },

  // Create payment
  createPayment: async (paymentData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/payment/create-snap`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(paymentData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Create payment failed");
      }

      return data;
    } catch (error) {
      console.error("Create payment error:", error);
      throw error;
    }
  },

  // Payment notification
  paymentNotification: async (notificationData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/payment/notification`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(notificationData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Payment notification failed");
      }

      return data;
    } catch (error) {
      console.error("Payment notification error:", error);
      throw error;
    }
  },

  // Chatbot
  chat: async (message) => {
    try {
      const response = await fetch(`${API_BASE_URL}/chat`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({ message }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Chat failed");
      }

      return data;
    } catch (error) {
      console.error("Chat error:", error);
      throw error;
    }
  },
};

// Export default untuk compatibility
export default apiService;

// Helper function to format register data according to backend requirements
export const formatRegisterData = (formData) => {
  return {
    email: formData.email,
    emailVisibility: true,
    password: formData.password,
    passwordConfirm: formData.password,
    name: formData.name,
    verified: false,
  };
};

// Helper function to format login data according to backend requirements
export const formatLoginData = (formData) => {
  return {
    email: formData.email,
    password: formData.password,
  };
};

// Helper function to format booking data according to backend requirements
export const formatBookingData = (personalData, selectedPackages = []) => {
  // Get current user data from localStorage
  const currentUser = apiService.getCurrentUser();
  // Gunakan user_id dari user yang login, fallback ke default jika tidak ada
  const userId = currentUser?.id || "64e0e7231ydhb";

  console.log("Data personal yang akan diformat:", personalData);
  console.log("User saat ini:", currentUser);

  // Format tanggal dari YYYY-MM-DD (input date format) tetap sebagai YYYY-MM-DD
  let formattedDate = personalData.activityDate || "";

  // Jika tanggal dalam format MM/DD/YYYY, konversi ke YYYY-MM-DD
  if (formattedDate && formattedDate.includes("/")) {
    const [month, day, year] = formattedDate.split("/");
    formattedDate = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
  }

  // Format data sesuai dengan backend requirement
  const formattedData = {
    name: personalData.name || "",
    nomortlp: personalData.phone || "",
    tanggal_kegiatan: formattedDate,
    hotel: personalData.hotel || "",
    transport: personalData.needTransport ? "Yes" : "No", // Backend menggunakan "Yes"/"No"
    transport_type: personalData.transportType || "Medium Car",
    // Coba berbagai kemungkinan field name untuk nomor sopir
    nomortlpdriver: personalData.needTransport
      ? null // Kirim null jika butuh transport
      : personalData.driverPhone || "081999008777", // Pastikan ada nilai
    nomor_sopir: personalData.needTransport
      ? null
      : personalData.driverPhone || "081999008777",
    driver_phone: personalData.needTransport
      ? null
      : personalData.driverPhone || "081999008777",
    user_id: userId,
  };

  console.log("Data yang sudah diformat:", formattedData);
  return formattedData;
};
