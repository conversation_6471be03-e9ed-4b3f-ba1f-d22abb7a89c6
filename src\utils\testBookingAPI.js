// Test utility untuk debugging booking API
const API_BASE_URL = "http://13.214.188.83:3000";

// Test data yang sama seperti di Postman yang berhasil
const testBookingData = {
  name: "galih",
  nomortlp: "081234567890",
  tanggal_kegiatan: "2025-08-23",
  hotel: "bintaro",
  transport: "Ya",
  transport_type: "Medium Car",
  user_id: "64e0e7231ydhb"
};

// Function untuk test booking API
export const testBookingAPI = async () => {
  console.log("=== TESTING BOOKING API ===");
  
  try {
    // Test 1: Coba endpoint /booking
    console.log("Testing endpoint: /booking");
    const response1 = await fetch(`${API_BASE_URL}/booking`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testBookingData),
    });
    
    console.log("Response status:", response1.status);
    console.log("Response headers:", Object.fromEntries(response1.headers.entries()));
    
    const data1 = await response1.text();
    console.log("Response data:", data1);
    
    if (response1.ok) {
      console.log("✅ Endpoint /booking berhasil!");
      return { success: true, endpoint: "/booking", data: data1 };
    }
    
  } catch (error) {
    console.error("❌ Error testing /booking:", error);
  }
  
  try {
    // Test 2: Coba endpoint PocketBase standard
    console.log("\nTesting endpoint: /api/collections/booking_uad/records");
    const response2 = await fetch(`${API_BASE_URL}/api/collections/booking_uad/records`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testBookingData),
    });
    
    console.log("Response status:", response2.status);
    console.log("Response headers:", Object.fromEntries(response2.headers.entries()));
    
    const data2 = await response2.text();
    console.log("Response data:", data2);
    
    if (response2.ok) {
      console.log("✅ Endpoint PocketBase berhasil!");
      return { success: true, endpoint: "/api/collections/booking_uad/records", data: data2 };
    }
    
  } catch (error) {
    console.error("❌ Error testing PocketBase endpoint:", error);
  }
  
  try {
    // Test 3: Coba endpoint lain yang mungkin
    console.log("\nTesting endpoint: /api/booking");
    const response3 = await fetch(`${API_BASE_URL}/api/booking`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testBookingData),
    });
    
    console.log("Response status:", response3.status);
    console.log("Response headers:", Object.fromEntries(response3.headers.entries()));
    
    const data3 = await response3.text();
    console.log("Response data:", data3);
    
    if (response3.ok) {
      console.log("✅ Endpoint /api/booking berhasil!");
      return { success: true, endpoint: "/api/booking", data: data3 };
    }
    
  } catch (error) {
    console.error("❌ Error testing /api/booking:", error);
  }
  
  console.log("❌ Semua endpoint gagal!");
  return { success: false };
};

// Function untuk test server connectivity
export const testServerConnection = async () => {
  console.log("=== TESTING SERVER CONNECTION ===");
  
  try {
    const response = await fetch(`${API_BASE_URL}/`, {
      method: "GET",
    });
    
    console.log("Server response status:", response.status);
    const data = await response.text();
    console.log("Server response:", data);
    
    return response.ok;
  } catch (error) {
    console.error("❌ Server connection failed:", error);
    return false;
  }
};

// Function untuk test dengan auth token
export const testBookingWithAuth = async () => {
  console.log("=== TESTING BOOKING WITH AUTH ===");
  
  const token = localStorage.getItem("authToken");
  console.log("Auth token:", token ? "Found" : "Not found");
  
  if (!token) {
    console.log("❌ No auth token found");
    return { success: false, error: "No auth token" };
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}/booking`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(testBookingData),
    });
    
    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    const data = await response.text();
    console.log("Response data:", data);
    
    return { 
      success: response.ok, 
      status: response.status, 
      data: data 
    };
    
  } catch (error) {
    console.error("❌ Error testing with auth:", error);
    return { success: false, error: error.message };
  }
};
