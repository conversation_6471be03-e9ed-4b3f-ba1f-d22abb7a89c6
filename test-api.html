<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test Tool</h1>
        
        <div class="test-section">
            <h2>Server Connection Test</h2>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <div id="server-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>Booking API Test</h2>
            <button onclick="testBookingEndpoint()">Test /booking Endpoint</button>
            <button onclick="testPocketBaseEndpoint()">Test PocketBase Endpoint</button>
            <div id="booking-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>Test Data</h2>
            <pre id="test-data"></pre>
        </div>
    </div>

    <script>
        const API_BASE_URL = "http://*************:3000";
        
        const testData = {
            name: "galih",
            nomortlp: "081234567890",
            tanggal_kegiatan: "2025-08-23",
            hotel: "bintaro",
            transport: "Ya",
            transport_type: "Medium Car",
            user_id: "64e0e7231ydhb"
        };
        
        // Display test data
        document.getElementById('test-data').textContent = JSON.stringify(testData, null, 2);
        
        async function testServerConnection() {
            const resultDiv = document.getElementById('server-result');
            resultDiv.innerHTML = 'Testing server connection...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/`, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                const data = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ Server Connection Successful</strong><br>
                            Status: ${response.status}<br>
                            Response: ${data}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ Server Error</strong><br>
                            Status: ${response.status}<br>
                            Response: ${data}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ Connection Failed</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testBookingEndpoint() {
            const resultDiv = document.getElementById('booking-result');
            resultDiv.innerHTML = 'Testing /booking endpoint...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/booking`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                    mode: 'cors'
                });
                
                const contentType = response.headers.get('content-type');
                let data;
                
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    data = await response.text();
                }
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ /booking Endpoint Successful</strong><br>
                            Status: ${response.status}<br>
                            Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ /booking Endpoint Error</strong><br>
                            Status: ${response.status}<br>
                            Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ /booking Request Failed</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
        
        async function testPocketBaseEndpoint() {
            const resultDiv = document.getElementById('booking-result');
            resultDiv.innerHTML = 'Testing PocketBase endpoint...';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/collections/booking_uad/records`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                    mode: 'cors'
                });
                
                const contentType = response.headers.get('content-type');
                let data;
                
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    data = await response.text();
                }
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ PocketBase Endpoint Successful</strong><br>
                            Status: ${response.status}<br>
                            Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ PocketBase Endpoint Error</strong><br>
                            Status: ${response.status}<br>
                            Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ PocketBase Request Failed</strong><br>
                        Error: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
